const { app, BrowserWindow, ipcMain, Menu, globalShortcut, shell } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

// 导入服务模块
const ModelService = require('./services/model-service');
const ConversationService = require('./services/conversation-service');
const MCPService = require('./services/mcp-service');
const PythonProcessManager = require('./services/python-process-manager');
const SystemService = require('./services/system-service');

// 全局变量
let mainWindow;
let modelService;
let conversationService;
let mcpService;
let pythonManager;
let systemService;

/**
 * 创建主窗口
 */
function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../resources/app_icon.png'),
    show: false // 先不显示，等待加载完成
  });

  // 加载应用
  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, '../view/build/index.html')}`;

  mainWindow.loadURL(startUrl);

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 开发模式下打开开发者工具
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 阻止新窗口打开，改为在默认浏览器中打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

/**
 * 初始化服务
 */
async function initializeServices() {
  try {
    console.log('正在初始化服务...');

    // 初始化模型服务
    modelService = new ModelService();
    await modelService.initialize();

    // 初始化会话服务
    conversationService = new ConversationService();
    await conversationService.initialize();

    // 初始化MCP服务
    mcpService = new MCPService();
    await mcpService.initialize();

    // 初始化Python进程管理器
    pythonManager = new PythonProcessManager();
    await pythonManager.initialize();

    // 初始化系统服务
    systemService = new SystemService();
    await systemService.initialize();

    console.log('所有服务初始化完成');
  } catch (error) {
    console.error('服务初始化失败:', error);
  }
}

/**
 * 注册IPC处理器
 */
function registerIpcHandlers() {
  // 模型管理相关
  ipcMain.handle('model:list', async () => {
    return await modelService.getAvailableModels();
  });

  ipcMain.handle('model:test', async (event, provider, apiKey) => {
    return await modelService.testConnection(provider, apiKey);
  });

  ipcMain.handle('model:chat', async (event, params) => {
    return await modelService.chatCompletion(params);
  });

  // 会话管理相关
  ipcMain.handle('conversation:list', async () => {
    return await conversationService.getConversations();
  });

  ipcMain.handle('conversation:create', async (event, params) => {
    return await conversationService.createConversation(params);
  });

  ipcMain.handle('conversation:addMessage', async (event, conversationId, message) => {
    return await conversationService.addMessage(conversationId, message);
  });

  ipcMain.handle('conversation:delete', async (event, conversationId) => {
    return await conversationService.deleteConversation(conversationId);
  });

  // MCP管理相关
  ipcMain.handle('mcp:servers', async () => {
    return await mcpService.getServers();
  });

  ipcMain.handle('mcp:tools', async () => {
    return await mcpService.getTools();
  });

  ipcMain.handle('mcp:execute', async (event, serverId, toolName, params) => {
    return await mcpService.executeTool(serverId, toolName, params);
  });

  // Python插件相关
  ipcMain.handle('python:call', async (event, method, params) => {
    return await pythonManager.callPython(method, params);
  });

  // 系统功能相关
  ipcMain.handle('system:openFile', async () => {
    return await systemService.openFileDialog();
  });

  ipcMain.handle('system:saveFile', async (event, content, filename) => {
    return await systemService.saveFileDialog(content, filename);
  });

  ipcMain.handle('system:notification', async (event, title, body) => {
    return await systemService.showNotification(title, body);
  });

  // 流式数据处理
  ipcMain.on('stream:start', (event, streamId) => {
    // 开始流式处理
  });

  ipcMain.on('stream:stop', (event, streamId) => {
    // 停止流式处理
  });
}

/**
 * 创建应用菜单
 */
function createMenu() {
  const template = [
    {
      label: 'JIMU',
      submenu: [
        {
          label: '关于 JIMU',
          role: 'about'
        },
        { type: 'separator' },
        {
          label: '隐藏 JIMU',
          accelerator: 'Command+H',
          role: 'hide'
        },
        {
          label: '隐藏其它',
          accelerator: 'Command+Shift+H',
          role: 'hideothers'
        },
        {
          label: '显示全部',
          role: 'unhide'
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Command+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' },
        { label: '全选', accelerator: 'CmdOrCtrl+A', role: 'selectall' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: '强制重新加载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: '切换开发者工具', accelerator: process.platform === 'darwin' ? 'Alt+Command+I' : 'Ctrl+Shift+I', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: '切换全屏', accelerator: process.platform === 'darwin' ? 'Ctrl+Command+F' : 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: '窗口',
      submenu: [
        { label: '最小化', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: '关闭', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

/**
 * 注册全局快捷键
 */
function registerGlobalShortcuts() {
  // 全局快捷键：显示/隐藏窗口
  globalShortcut.register('CommandOrControl+Shift+J', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
        mainWindow.focus();
      }
    }
  });

  // 全局快捷键：快速聊天
  globalShortcut.register('CommandOrControl+Shift+C', () => {
    if (mainWindow) {
      mainWindow.show();
      mainWindow.focus();
      // 发送事件到渲染进程，聚焦到输入框
      mainWindow.webContents.send('shortcut:quickChat');
    }
  });
}

/**
 * 应用准备就绪
 */
app.whenReady().then(async () => {
  console.log('Electron 应用启动中...');

  // 创建窗口
  createWindow();

  // 创建菜单
  createMenu();

  // 注册全局快捷键
  registerGlobalShortcuts();

  // 注册IPC处理器
  registerIpcHandlers();

  // 初始化服务（异步）
  initializeServices().catch(console.error);

  // macOS 特定处理
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

/**
 * 所有窗口关闭时退出应用
 */
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

/**
 * 应用即将退出时的清理工作
 */
app.on('before-quit', async () => {
  console.log('应用正在退出，清理资源...');

  // 注销全局快捷键
  globalShortcut.unregisterAll();

  // 关闭服务
  try {
    if (pythonManager) {
      await pythonManager.shutdown();
    }
    if (mcpService) {
      await mcpService.shutdown();
    }
    if (conversationService) {
      await conversationService.shutdown();
    }
    if (modelService) {
      await modelService.shutdown();
    }
    if (systemService) {
      await systemService.shutdown();
    }
  } catch (error) {
    console.error('关闭服务时出错:', error);
  }
});

/**
 * 安全处理
 */
app.on('web-contents-created', (event, contents) => {
  // 阻止创建新的webContents
  contents.on('new-window', (event, url) => {
    event.preventDefault();
    shell.openExternal(url);
  });

  // 阻止导航到外部URL
  contents.on('will-navigate', (event, url) => {
    if (url !== contents.getURL()) {
      event.preventDefault();
      shell.openExternal(url);
    }
  });
});