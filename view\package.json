{"name": "jimu-app", "version": "0.1.0", "private": true, "homepage": ".", "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.24.6", "axios": "^1.6.7", "bootstrap": "^5.3.6", "china-division": "^2.7.0", "date-fns": "^4.1.0", "fabric": "^6.7.0", "html2canvas": "^1.4.1", "http-proxy-middleware": "^2.0.6", "localforage": "^1.10.0", "marked-highlight": "^2.2.1", "mermaid": "^11.6.0", "moment": "^2.30.1", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-china-division": "^0.2.1", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-monaco-editor": "^0.58.0", "react-router-dom": "^7.5.0", "react-scripts": "^5.0.1", "react-syntax-highlighter": "^15.6.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "rehype-highlight": "^7.0.2", "rehype-react": "^8.0.0", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "styled-components": "^6.1.17", "sweetalert2": "^11.21.0", "unified": "^11.0.5", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"cross-env": "^7.0.3"}, "proxy": "http://localhost:8080"}